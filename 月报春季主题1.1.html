<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中节能实业公司技术月刊（第五期）- 春季主题</title>
    
    <!-- TailwindCSS 3.0+ via CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome Icons via CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    
    <!-- Three.js Library via CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <style>
        /* Smooth scrolling for anchor links */
        html {
            scroll-behavior: smooth;
        }
        /* Custom scrollbar for Spring Theme */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f0fdf4; /* green-50 */
        }
        ::-webkit-scrollbar-thumb {
            background: #22c55e; /* green-500 */
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #16a34a; /* green-600 */
        }

        /* Modal backdrop blur effect */
        .modal-backdrop-custom {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        /* Animation initial state */
        .reveal-element {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        /* State after element enters viewport */
        .revealed {
            opacity: 1;
            transform: translateY(0);
        }

        /* Style for the Three.js background canvas */
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -20; /* Place it behind everything */
        }
    </style>
</head>
<!-- THEME CHANGE: Body background and text color updated for Spring theme -->
<body class="bg-gradient-to-br from-green-100 via-lime-50 to-white text-slate-800 font-sans antialiased">

    <!-- Three.js Background Canvas -->
    <canvas id="bg-canvas"></canvas>

    <div id="root" class="max-w-7xl mx-auto p-4 md:p-8">
        <main class="grid grid-cols-1 md:grid-cols-4 gap-6">

            <!-- Header Card (Final Aligned Layout) -->
            <!-- THEME CHANGE: Card styles updated for a light, glassy look -->
            <div id="page-top" class="md:col-span-4 p-8 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl relative overflow-hidden reveal-element">
                <!-- THEME CHANGE: Decorative blur updated -->
                <div class="absolute -top-1/4 -right-1/4 w-96 h-96 bg-emerald-400/20 rounded-full blur-3xl -z-10"></div>
                
                <div class="flex justify-between items-end">
                    <!-- Left Side: Title & Navigation -->
                    <div>
                        <!-- THEME CHANGE: Title gradient updated for dark text -->
                        <h1 class="text-5xl md:text-6xl font-bold tracking-tighter bg-clip-text text-transparent bg-gradient-to-br from-slate-900 to-slate-600">中节能实业公司技术月刊</h1>
                        <nav class="mt-8">
                            <ul class="flex justify-start items-center space-x-4 md:space-x-8">
                                <!-- THEME CHANGE: Nav link colors updated -->
                                <li><a href="#policy-section" class="text-slate-600 hover:text-emerald-500 transition-colors duration-300 font-medium">政策动向</a></li>
                                <li><a href="#tech-section" class="text-slate-600 hover:text-emerald-500 transition-colors duration-300 font-medium">技术动态</a></li>
                                <li><a href="#case-study-section" class="text-slate-600 hover:text-emerald-500 transition-colors duration-300 font-medium">案例分析</a></li>
                                <li><a href="#conclusion-section" class="text-slate-600 hover:text-emerald-500 transition-colors duration-300 font-medium">结语</a></li>
                                <li><button id="issue-selector-btn-main" class="text-slate-600 hover:text-emerald-500 transition-colors duration-300 font-medium">往期月刊</button></li>
                                <!-- UPDATED: Podcast button added -->
                                <li><button id="podcast-btn" class="text-slate-600 hover:text-emerald-500 transition-colors duration-300 font-medium">播客</button></li>
                            </ul>
                        </nav>
                    </div>
                    
                    <!-- Right Side: Issue Info -->
                    <div class="relative flex items-end space-x-6">
                        <div class="text-right flex-shrink-0">
                            <!-- THEME CHANGE: Accent color updated -->
                            <div class="text-7xl md:text-8xl font-bold text-emerald-500">5</div>
                            <!-- THEME CHANGE: Text color updated for readability -->
                            <p class="text-slate-500 text-sm">2025年第5期 / 总第5期</p>
                            <p class="text-slate-500 text-sm mt-1">技术中心 2025年6月</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Introduction Card -->
            <div class="md:col-span-4 p-8 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element">
                <!-- THEME CHANGE: Accent color updated -->
                <h2 class="text-2xl font-semibold text-emerald-600 mb-4">本期导言</h2>
                <!-- THEME CHANGE: Text color updated for readability -->
                <p class="text-slate-700 leading-relaxed">2025年5月至6月，国内节能环保及绿色建筑领域的政策导向和技术实践持续深化。国家层面，通过发布关于有序推动绿电直连、深化提升“获得电力”服务水平等重要文件，进一步强调了能源结构的绿色转型和市场化改革。地方政府亦积极响应，江苏、广东、深圳、无锡等地纷纷出台或推进地方性绿色建筑法规、零碳园区建设方案及先进绿色技术试点项目，共同推动能源效率提升和可再生能源的广泛应用。</p>
                <p class="text-slate-700 leading-relaxed mt-4">本期月报旨在梳理并解读此期间国家及地方层面的相关政策，重点关注分布式光伏发电技术的最新进展与应用。报告将选取对公司转型具有重要参考价值的关键技术进行推介，并通过具体项目案例，展示其在写字楼、产业园区厂房及公共基础设施等不同类型建筑中的实际应用与节能减排效果，为公司在绿色低碳发展道路上的战略决策和技术布局提供参考。</p>
            </div>
            
            <!-- Content Teaser Card (Summarized) -->
            <div class="md:col-span-4 p-8 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element">
                 <h2 class="text-2xl font-semibold text-emerald-600 mb-6">内容提要</h2>
                 <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                     <div class="flex items-start space-x-4">
                         <div class="text-5xl font-bold text-emerald-500">1</div>
                         <p class="text-slate-700"><strong>关键政策引领转型</strong>：绿电直连与“获得电力”服务优化，为企业绿电采购铺平道路。</p>
                     </div>
                     <div class="flex items-start space-x-4">
                         <div class="text-5xl font-bold text-emerald-500">2</div>
                         <p class="text-slate-700"><strong>地方绿色创新实践</strong>：苏、粤、深、鲁、锡等地BIPV、光储直柔、零碳园区建设加速。</p>
                     </div>
                     <div class="flex items-start space-x-4">
                         <div class="text-5xl font-bold text-emerald-500">3</div>
                         <p class="text-slate-700"><strong>技术标准与前沿动态</strong>：低碳数据中心、零碳园区等新标准发布，引领行业规范发展。</p>
                     </div>
                     <div class="flex items-start space-x-4">
                         <div class="text-5xl font-bold text-emerald-500">4</div>
                         <p class="text-slate-700"><strong>分布式光伏发展聚焦</strong>：创新模式与调控技术涌现，推动高水平消纳与应用。</p>
                     </div>
                 </div>
            </div>

            <!-- Section Title: Policy -->
            <div id="policy-section" class="md:col-span-4 reveal-element pt-8">
                <h2 class="text-3xl font-bold tracking-tight text-slate-800 sm:text-4xl mt-8 mb-2 flex items-center gap-3">
                    <i class="fa-solid fa-scroll text-emerald-500"></i>
                    一、国家及地方政策动向
                </h2>
                <div class="h-1 bg-gradient-to-r from-emerald-400/70 to-transparent w-1/3"></div>
            </div>

            <!-- Policy Cards -->
            <div class="md:col-span-2 p-6 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element flex flex-col">
                <div class="flex-grow">
                    <h3 class="font-semibold text-lg text-slate-900">1. 关于有序推动绿电直连发展有关事项的通知</h3>
                    <p class="text-sm text-slate-500 mt-1">国家发改委、国家能源局 | 2025年5月21日</p>
                    <p class="text-sm text-slate-700 mt-4 leading-6">该通知明确了“绿电直连”的定义，即风电、太阳能发电、生物质发电等新能源不直接接入公共电网，而是通过直连线路向单一电力用户供给绿色电力，并能够实现供给电量的清晰物理溯源。适用范围包括并网型和离网型两类项目，其中并网型项目作为整体接入公共电网，电源应接入用户和公共电网产权分界点的用户侧。特别指出，若直连电源为分布式光伏，则需按照《分布式光伏发电开发建设管理办法》等相关政策执行。通知的目标是满足企业绿色用能需求，提升新能源就近就地消纳水平，并遵循安全优先、绿色友好、权责对等、源荷匹配的原则进行建设和运行。</p>
                </div>
                <div class="mt-4 flex gap-4">
                    <button class="modal-trigger text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors" data-modal-id="policy-1">政策解读 <i class="fa-solid fa-magnifying-glass-plus ml-1"></i></button>
                    <a href="https://www.ndrc.gov.cn/xxgk/zcfb/tz/202505/t20250530_1398138.html" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                </div>
            </div>

            <div class="md:col-span-2 p-6 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element flex flex-col">
                <div class="flex-grow">
                    <h3 class="font-semibold text-lg text-slate-900">2. 关于深化提升“获得电力”服务水平全面打造现代化用电营商环境的意见</h3>
                    <p class="text-sm text-slate-500 mt-1">国家发改委、国家能源局 | 2025年6月3日</p>
                    <p class="text-sm text-slate-700 mt-4 leading-6">该意见旨在到2029年基本建成办电便捷化、供电高质化、用电绿色化、服务普惠化、监管协同化的现代化用电营商环境。意见强调促进绿色低碳发展，包括通过农网改造和柔性配网技术提升电网对分布式新能源的接纳能力。国家在“十四五”期间已累计投入超过1700亿元用于农网建设，2025年计划投资将达到312亿元，以支持分布式能源发展。</p>
                </div>
                <div class="mt-4 flex gap-4">
                    <button class="modal-trigger text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors" data-modal-id="policy-2">政策解读 <i class="fa-solid fa-magnifying-glass-plus ml-1"></i></button>
                    <a href="https://www.ndrc.gov.cn/xxgk/zcfb/ghxwj/202506/t20250603_1398199.html" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                </div>
            </div>
            
            <div class="md:col-span-4 p-6 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element">
                <h3 class="font-semibold text-lg text-slate-900">3. 《分布式光伏发电开发建设管理办法》</h3>
                <p class="text-sm text-slate-500 mt-1">国家能源局 | 2025年1月23日发布</p>
                <p class="text-sm text-slate-700 mt-4 leading-6">该办法对分布式光伏发电的定义、分类（自然人户用、非自然人户用、一般工商业、大型工商业）、行业管理职责、备案原则（“谁投资、谁备案”）、建设管理、电网接入（包括电网承载力评估及可开放容量季度发布机制）以及运行管理（包括“可观、可测、可调、可控”的“四可”能力要求）等全生命周期各环节进行了全面规范。同时，对办法发布前已备案且于2025年5月1日前并网投产项目的政策衔接做出了规定。</p>
                <div class="mt-4 flex gap-4">
                    <button class="modal-trigger text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors" data-modal-id="policy-3">政策解读 <i class="fa-solid fa-magnifying-glass-plus ml-1"></i></button>
                    <a href="https://www.nea.gov.cn/20250123/112c5b199c5f45dd8e7ac93c9f5e4eaf/c.html" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                </div>
            </div>
            
            <div class="md:col-span-4 p-6 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element">
                <h3 class="font-semibold text-lg text-slate-900">4. 《关于印发源网荷储一体化试点实施细则的通知》</h3>
                <p class="text-sm text-slate-500 mt-1">山东省能源局 | 2025年3月5日</p>
                <p class="text-sm text-slate-700 mt-4 leading-6">源网荷储一体化试点工作按照4类模式组织实施，分别为就地就近消纳、绿电交易、虚拟电厂、分布式自发自用。《通知》提出，新能源就地就近消纳模式适用于周边新能源资源条件较好、对绿电直连有明确要求的出口型企业，项目应以提升新能源消纳水平为目标。虚拟电厂模式适用于依托未纳入调度管理范围的分布式电源、用户侧或分布式储能、可调节负荷等各类资源建设的一体化项目，通过聚合为虚拟电厂参与电力市场交易，实现源网荷储灵活互动。绿电交易模式要求具备电力数据实时监控、电力有功及无功自动控制、电力功率及负荷数据预测、市场出清及调度指令接收分解下发等功能。</p>
                <div class="mt-4 flex gap-4">
                    <button class="modal-trigger text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors" data-modal-id="policy-4">政策解读 <i class="fa-solid fa-magnifying-glass-plus ml-1"></i></button>
                    <a href="http://nyj.shandong.gov.cn/art/2025/3/5/art_59960_10308033.html" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                </div>
            </div>

            <div class="md:col-span-1 p-6 flex flex-col justify-between bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element">
                <div>
                    <h3 class="font-semibold text-slate-900">5. 江苏省绿色建筑发展条例</h3>
                    <p class="text-sm text-slate-500 mt-1">江苏省人大 | 2025年5月审议</p>
                    <p class="text-sm text-slate-700 mt-4">新修订的条例将碳排放管理全面融入绿色建筑发展，新增条款要求建立建筑领域碳排放统计核算、产品碳标识认证、产品碳足迹管理体系和制度，健全碳排放交易工作机制。县级以上地方政府需制定本行政区建筑领域碳排放控制目标。同时，提高了政府投资公共建筑的绿色建筑等级要求（二星级以上），并规定建筑工程设计文件需包含碳排放分析报告等绿色建筑专篇。</p>
                </div>
                <div class="mt-4 flex gap-4">
                    <button class="modal-trigger text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors" data-modal-id="policy-5">政策解读 <i class="fa-solid fa-magnifying-glass-plus ml-1"></i></button>
                    <a href="http://jsj.lyg.gov.cn/lygszfj/kysj/content/a5032886-5fb6-41a4-bbd9-f1e0f8e513eb.html" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                </div>
            </div>

            <div class="md:col-span-1 p-6 flex flex-col justify-between bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element">
                <div>
                    <h3 class="font-semibold text-slate-900">6. 广东省公共机构节能降碳工作安排</h3>
                    <p class="text-sm text-slate-500 mt-1">广东省能源局 | 2025年5月26日</p>
                    <p class="text-sm text-slate-700 mt-4">通知鼓励公共机构因地制宜推广太阳能、地热能、生物质能等可再生能源利用，持续推广分布式光伏、新能源汽车及充电基础设施建设。扩大“绿电”应用规模，鼓励通过购买绿证等方式促进非化石能源消费。同时，要求加快制定或修订《广东省公共机构能源资源消耗限额》等标准。</p>
                </div>
                <div class="mt-4 flex gap-4">
                    <button class="modal-trigger text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors" data-modal-id="policy-6">政策解读 <i class="fa-solid fa-magnifying-glass-plus ml-1"></i></button>
                    <a href="http://drc.gd.gov.cn/snyj/tzgg/content/post_4716102.html" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                </div>
            </div>
            
            <div class="md:col-span-1 p-6 flex flex-col justify-between bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element">
                <div>
                    <h3 class="font-semibold text-slate-900">7. 深圳市BIPV和“光储直柔”建筑试点申报</h3>
                    <p class="text-sm text-slate-500 mt-1">深圳市住建局 | 2025年4月17日</p>
                    <p class="text-sm text-slate-700 mt-4">该通知旨在征集2025年度的建筑光伏一体化（BIPV）和“光储直柔”（光伏-储能-直流-柔性用电）建筑试点项目。BIPV项目要求光伏组件作为建材与建筑集成；“光储直柔”项目则需整合光伏发电、储能、直流配电、柔性用电中至少两项技术。项目申报分为两批，截止日期分别为5月15日和9月30日。</p>
                </div>
                <div class="mt-4 flex gap-4">
                    <button class="modal-trigger text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors" data-modal-id="policy-7">政策解读 <i class="fa-solid fa-magnifying-glass-plus ml-1"></i></button>
                    <a href="https://zjj.sz.gov.cn/xxgk/tzgg/content/post_12130604.html" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                </div>
            </div>
            
            <div class="md:col-span-1 p-6 flex flex-col justify-between bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element">
                <div>
                    <h3 class="font-semibold text-slate-900">8. 无锡市零碳园区建设三年行动方案</h3>
                    <p class="text-sm text-slate-500 mt-1">无锡市政府 | 2025年5月20日</p>
                    <p class="text-sm text-slate-700 mt-4">该方案提出在产业园区推广“光储直柔”、建筑信息模型（BIM）等先进绿色技术的集成应用。鼓励对园区内既有厂房、办公用房和生活用房进行绿色低碳改造。支持园区数据中心、物流仓储、供电供热供水等基础设施的绿色化、循环化改造。</p>
                </div>
                <div class="mt-4 flex gap-4">
                    <button class="modal-trigger text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors" data-modal-id="policy-8">政策解读 <i class="fa-solid fa-magnifying-glass-plus ml-1"></i></button>
                    <a href="http://www.wuxi.gov.cn/doc/2025/05/20/4576900.shtml" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                </div>
            </div>

            <!-- Section Title: Tech & Standards -->
            <div id="tech-section" class="md:col-span-4 reveal-element pt-8">
                <h2 class="text-3xl font-bold tracking-tight text-slate-800 sm:text-4xl mt-8 mb-2 flex items-center gap-3">
                    <i class="fa-solid fa-microchip text-emerald-500"></i>
                    二、新技术与标准发展动态
                </h2>
                <div class="h-1 bg-gradient-to-r from-emerald-400/70 to-transparent w-1/3"></div>
            </div>

            <div class="md:col-span-4 p-8 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element">
                <h3 class="text-xl font-semibold text-slate-900 mb-4">新发布的技术标准 (中国建筑节能协会)</h3>
                <div class="space-y-4">
                    <div class="p-4 border border-slate-200/80 rounded-xl">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="font-semibold text-slate-800">《既有建筑用发泡陶瓷保温装饰板外墙外保温系统技术规程》</p>
                                <p class="text-sm text-slate-600 mt-1">该标准专注于存量建筑的节能改造。它规范了采用发泡陶瓷保温装饰板进行外墙外保温系统的设计、材料、施工及验收，强调在改造中无需剔除原有保温层，从而节约材料、减少废弃物。</p>
                            </div>
                            <a href="https://www.cabee.org/site/content/25407.html" target="_blank" class="flex-shrink-0 ml-4 text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                        </div>
                    </div>
                    <div class="p-4 border border-slate-200/80 rounded-xl">
                        <div class="flex justify-between items-start">
                             <div>
                                <p class="font-semibold text-slate-800">《低碳数据中心设计导则》</p>
                                <p class="text-sm text-slate-600 mt-1">该导则为数据中心这一高耗能建筑类型提供全生命周期的低碳设计技术体系。其核心目标包括推动可再生能源利用率超过30%，并整合了“光储直柔”、液冷技术、数字孪生等12类前沿技术。</p>
                            </div>
                            <a href="https://www.cabee.org/site/content/25406.html" target="_blank" class="flex-shrink-0 ml-4 text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                        </div>
                    </div>
                    <div class="p-4 border border-slate-200/80 rounded-xl">
                         <div class="flex justify-between items-start">
                             <div>
                                <p class="font-semibold text-slate-800">《零碳园区评价标准（试行）》</p>
                                <p class="text-sm text-slate-600 mt-1">此标准适用于新建和改建的低碳、近零碳及零碳产业园区和民用园区。标准提出了园区碳减排率、人均碳排放等评价指标，并对园区规划、能源系统、碳抵消机制及运营管理等方面给出了指导性措施。</p>
                            </div>
                            <a href="https://www.cabee.org/site/content/25405.html" target="_blank" class="flex-shrink-0 ml-4 text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="md:col-span-2 p-8 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element relative overflow-hidden flex flex-col">
                <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-emerald-400/10 to-transparent -z-10"></div>
                <div class="flex-grow">
                    <h3 class="text-xl font-semibold text-slate-900 mb-4">新技术及应用：1、分布式光伏发电</h3>
                    <p class="text-sm text-slate-700 leading-6">分布式光伏发电，依据国家能源局2025年1月发布的《分布式光伏发电开发建设管理办法》，是指在用户侧开发、在配电网接入、原则上在配电网系统就近平衡调节的光伏发电设施。其类型主要分为自然人户用、非自然人户用、一般工商业和大型工商业四种。并网模式包括全额上网、全部自发自用、自发自用余电上网，强调通过先进逆变器技术、储能系统集成、智能化调度和虚拟电厂（VPP）等手段，解决分布式光伏高渗透率带来的电网电压波动、潮流反送、调峰困难等问题，确保系统的“可观、可测、可调、可控”。</p>
                </div>
                 <button class="modal-trigger-multi mt-6 text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors self-start" data-modal-type="techCases">
                    查看近期项目案例 <i class="fa-solid fa-layer-group ml-1"></i>
                </button>
            </div>
            
            <div class="md:col-span-2 p-8 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element relative overflow-hidden flex flex-col">
                <div class="absolute top-0 right-0 w-full h-full bg-gradient-to-bl from-lime-400/10 to-transparent -z-10"></div>
                <div class="flex-grow">
                    <h3 class="text-xl font-semibold text-slate-900 mb-4">新技术及应用：2、光储直柔园区微电网</h3>
                    <p class="text-sm text-slate-700 leading-6">该技术是指在园区层面，集成光伏发电、储能系统、直流配电和柔性用电四种核心技术，构建高效、灵活、可靠的微电网系统。其目的是最大化本地可再生能源消纳，平抑负荷波动，提升能源利用效率和供电可靠性，并能与大电网灵活互动。</p>
                </div>
                 <button class="modal-trigger-multi mt-6 text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors self-start" data-modal-type="gcsfCases">
                    查看近期项目案例 <i class="fa-solid fa-layer-group ml-1"></i>
                </button>
            </div>

            <!-- Section Title: Case Study -->
            <div id="case-study-section" class="md:col-span-4 reveal-element pt-8">
                <h2 class="text-3xl font-bold tracking-tight text-slate-800 sm:text-4xl mt-8 mb-2 flex items-center gap-3">
                    <i class="fa-solid fa-chart-pie text-emerald-500"></i>
                    三、案例分析——分布式光伏发电专题
                </h2>
                <div class="h-1 bg-gradient-to-r from-emerald-400/70 to-transparent w-1/3"></div>
            </div>
            
            <div class="md:col-span-4 p-8 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl reveal-element flex flex-col">
                <div class="flex-grow">
                    <h3 class="text-xl font-semibold text-slate-900 mb-4">
                        <a href="https://www.nea.gov.cn/20250123/112c5b199c5f45dd8e7ac93c9f5e4eaf/c.html" target="_blank" class="text-slate-900 hover:text-emerald-600 transition-colors">《分布式光伏发电开发建设管理办法》</a>
                        （以下简称《办法》）核心技术要点解读
                    </h3>
                    <div class="space-y-3 text-sm text-slate-700 leading-6 mt-4">
                        <p><strong class="text-emerald-600">1.1、项目分类与技术规范的精细化：</strong>《办法》将分布式光伏细分为自然人户用、非自然人户用、一般工商业和大型工商业四种类型 。这种分类不仅明确了不同项目的适用场景，更重要的是针对不同类型项目，尤其是一般工商业（通常适用于写字楼、中小型厂房、学校、医院等）和大型工商业（适用于大型厂房、大型商业综合体等），在并网电压等级和容量上限方面做出了明确的技术规定 。这些清晰的技术参数为项目的前期规划和设备选型提供了直接依据，有助于避免因规模与电网条件不匹配而导致的并网困难或资源浪费。同时，《办法》及其<a href="http://www.nea.gov.cn/20250411/dca28f9186e848dda1d690d192fa50f8/20250411dca28f9186e848dda1d690d192fa50f8_113000de00999043e8a7fe4446dd6d97d8.pdf" target="_blank" class="text-emerald-500 hover:text-emerald-400 transition-colors">《问答》</a>对“建筑物及其附属场所应当位于同一用地红线范围内”的规定进行了详细解释，明确了附属场所包括建筑周边空地，并对同一红线内多个建筑对应不同用电户号时可分别备案等情况予以说明 ，为BIPV、停车场光伏等多样化场景的应用提供了政策空间。</p>
                        <p><strong class="text-emerald-600">1.2、并网模式与自用比例的引导：</strong>《办法》明确了分布式光伏的三种上网模式：全额上网、全部自发自用、自发自用余电上网 。对于工商业分布式光伏，一般工商业项目可选择全部自发自用或自发自用余电上网模式，后者模式下的年自发自用电量比例由各省级能源主管部门结合实际确定；大型工商业项目则原则上选择全部自发自用模式，但在电力现货市场连续运行地区，也可采用自发自用余电上网模式参与现货市场 。这种差异化的模式引导，旨在优先保障项目自用电需求，提高能源利用效率，并与电力市场化改革相衔接。省级主管部门对自用比例的确定权，则允许地方根据自身电力供需状况和产业特点进行灵活调整。</p>
                        <p><strong class="text-emerald-600">1.3、“四可”技术要求的全面推行：</strong>要求新建的分布式光伏项目（不分类型）均需实现“可观、可测、可调、可控”功能 ，是新《办法》中最具影响力的技术要求之一。这意味着所有新项目都必须配备相应的监测设备、通信接口和控制逻辑，使其运行状态能够被电网实时监测，发电出力能够根据电网调度指令进行调整。这一方面是为了提升配电网对大规模分布式电源的接纳能力和运行稳定性，另一方面也为分布式光伏参与虚拟电厂、电力辅助服务等更高级的市场应用奠定了技术基础。这一要求的落实，将推动智能逆变器、能量管理系统（EMS）、储能以及相关通信和控制技术的广泛应用。</p>
                        <p><strong class="text-emerald-600">1.4、备案管理与项目变更的规范化：</strong>《办法》坚持“谁投资、谁备案”的原则，并禁止非自然人投资项目以自然人名义备案，以规范市场秩序 。同时，对项目备案后的信息变更，特别是上网模式的变更（允许根据用户负荷和经营状况变更一次），也提供了明确的路径和要求 ，增强了项目运营的灵活性。</p>
                    </div>
                </div>
            </div>

            <div class="md:col-span-2 p-8 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl flex flex-col justify-between reveal-element">
                <div>
                    <h3 class="text-xl font-semibold text-slate-900 mb-2">国网山西大同分布式光伏柔性群调群控试点</h3>
                    <p class="text-slate-500 text-sm">背景：应对大规模户用光伏接入挑战</p>
                    <p class="text-slate-700 text-sm mt-4">成功对1.2万户分布式光伏实现柔性群调群控，通过提升采集频率、加装接入单元、制定差异化策略等技术手段，验证了高密度户用光伏区域智能调控的可行性。</p>
                </div>
                <button class="modal-trigger-multi mt-6 text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors self-start" data-modal-type="analysisCases" data-initial-index="0">
                    查看深度解析 <i class="fa-solid fa-magnifying-glass-chart ml-1"></i>
                </button>
            </div>
            
            <div class="md:col-span-2 p-8 bg-white/60 backdrop-blur-md border border-white/20 shadow-lg rounded-3xl flex flex-col justify-between reveal-element">
                <div>
                    <h3 class="text-xl font-semibold text-slate-900 mb-2">重庆“百万千瓦屋顶”行动中的工厂“光储充”一体化项目</h3>
                    <p class="text-slate-500 text-sm">模式：工业园区能源解决方案</p>
                    <p class="text-slate-700 text-sm mt-4">以长安汽车渝北工厂为例，将光伏、储能、充电桩一体化设计和智能管理，形成能源生产、存储、消费闭环，显著降低能源成本并提升绿电比例，辅以创新的金融政策支持。</p>
                </div>
                <button class="modal-trigger-multi mt-6 text-sm font-semibold text-emerald-600 hover:text-emerald-500 transition-colors self-start" data-modal-type="analysisCases" data-initial-index="1">
                    查看深度解析 <i class="fa-solid fa-magnifying-glass-chart ml-1"></i>
                </button>
            </div>

            <!-- Section Title: Conclusion -->
            <div id="conclusion-section" class="md:col-span-4 reveal-element pt-8">
                <h2 class="text-3xl font-bold tracking-tight text-slate-800 sm:text-4xl mt-8 mb-2 flex items-center gap-3">
                    <i class="fa-solid fa-flag-checkered text-emerald-500"></i>
                    结语
                </h2>
                <div class="h-1 bg-gradient-to-r from-emerald-400/70 to-transparent w-1/3 mb-6"></div>
                <p class="text-slate-700 leading-relaxed">2025年5月至6月期间，国内在节能环保和绿色建筑领域的政策执行与技术推广均呈现出加速态势。国家层面通过完善法规（如《分布式光伏发电开发建设管理办法》）和出台指导意见（如虚拟电厂发展），为行业发展设定了清晰的框架和目标。地方政府则积极响应，将国家战略转化为具体的行动计划、补贴政策和示范项目，形成了上下联动、协同推进的良好局面。</p>
                <p class="text-slate-700 leading-relaxed mt-4">分布式光伏发电无疑是当前新能源发展的重中之重，其应用场景不断拓展，技术要求也日趋严格和智能化（如“四可”能力的普及）。与此同时，以BIPV、“光储直柔”、中深层地热供暖为代表的绿色技术，正从示范走向更广泛的实际应用，尤其是在产业园区和公共建筑领域，展现出巨大的节能降碳潜力。</p>
                <p class="text-slate-700 leading-relaxed mt-4">展望未来，预计分布式能源的并网消纳和市场化交易将是政策持续关注的焦点，与之相关的智能电网技术、储能解决方案以及虚拟电厂等聚合模式将迎来快速发展期。</p>
            </div>

        </main>
    </div>

    <!-- Scroll to Top Button -->
    <a href="#page-top" id="scrollTopBtn" class="fixed right-8 top-1/2 -translate-y-1/2 bg-emerald-500/80 text-white w-12 h-12 rounded-full hidden items-center justify-center shadow-lg backdrop-blur-sm hover:bg-emerald-400 hover:scale-110 transition-all duration-300 z-50">
        <i class="fa-solid fa-arrow-up"></i>
    </a>

    <!-- Issue Selector Modal -->
    <div id="issue-modal" class="fixed inset-0 z-[60] items-center justify-center p-4 hidden">
        <!-- THEME CHANGE: Modal backdrop color -->
        <div id="issue-modal-backdrop" class="fixed inset-0 bg-green-900/40 modal-backdrop-custom"></div>
        <!-- THEME CHANGE: Modal content styles -->
        <div id="issue-modal-content" class="relative bg-white/80 backdrop-blur-xl border border-white/30 rounded-full shadow-2xl w-96 h-96 flex items-center justify-center transition-transform duration-500 ease-in-out transform -translate-x-full">
            <button id="issue-modal-close" class="absolute top-4 right-4 text-slate-500 hover:text-slate-800 transition-colors z-10"><i class="fa-solid fa-xmark fa-lg"></i></button>
            
             <div id="year-selector-container" class="absolute flex items-center justify-center gap-6">
                <button id="year-prev-btn" class="text-2xl transition-colors">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <span id="year-display" class="text-3xl font-bold text-slate-800 w-28 text-center"></span>
                <button id="year-next-btn" class="text-2xl transition-colors">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>

            <div id="month-wheel" class="absolute w-full h-full">
                <!-- Months will be generated here by JS -->
            </div>
        </div>
    </div>


    <!-- Standard Modal Structure -->
    <div id="modal-container" class="fixed inset-0 z-50 items-center justify-center p-4 hidden">
      <div class="fixed inset-0 bg-green-900/40 modal-backdrop-custom modal-close"></div>
      <div id="modal-content-wrapper" class="relative bg-white/90 backdrop-blur-xl border border-white/30 rounded-2xl shadow-xl w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div class="p-6 md:p-8">
            <div class="flex justify-between items-start mb-4">
                <h3 id="modal-title" class="text-xl font-bold text-emerald-600 pr-8"></h3>
                <button class="modal-close text-slate-500 hover:text-slate-800 transition-colors flex-shrink-0">
                    <i class="fa-solid fa-xmark fa-lg"></i>
                </button>
            </div>
            <div id="modal-body" class="text-slate-700 leading-relaxed space-y-4">
                <!-- Content will be injected here -->
            </div>
            <div id="modal-footer" class="mt-6 pt-4 border-t border-slate-200/80 hidden">
                 <!-- Multi-item navigation will be injected here -->
            </div>
        </div>
      </div>
    </div>


    <script>
    document.addEventListener('DOMContentLoaded', function() {

        // --- Data for Modals ---
        const modalData = {
            'policy-1': {
                title: '《绿电直连发展通知》政策解读',
                content: `<p>此项政策为企业用户直接采购和使用绿色电力提供了更为清晰的框架和操作指引，有助于提升可再生能源项目的市场化程度。通过允许具备条件的发电企业与用户直接交易，可以减少中间环节，降低用能成本，同时增强企业使用绿电的透明度和可追溯性。对于中节能实业公司而言，这可能为公司参与开发针对特定工业园区或大型企业的定制化绿电供应项目带来新的机遇，特别是在提供综合能源服务的转型过程中。通知中对“清晰物理溯源”的强调，反映出市场对绿电真实性的高度关注，这可能会催生对先进计量、追踪和认证技术的需求。企业在规划此类项目时，不仅要考虑发电技术本身，还需关注如何构建和管理直供线路及配套的溯源体系，这可能成为未来绿电项目的一个重要增值点和竞争优势。</p>`
            },
            'policy-2': {
                title: '《深化提升“获得电力”服务水平意见》政策解读',
                content: `<p>该政策的发布，体现了国家层面持续优化电力行业营商环境、提升服务效率的决心，这将直接惠及包括分布式光伏在内的各类电力项目开发者。其中，“用电绿色化”和对分布式新能源消纳能力提升的关注，为可再生能源的进一步发展提供了有力支撑。对于中节能实业公司而言，更为简化的办电流程和更为友好的并网服务，能够有效缩短项目开发周期，降低前期不确定性。特别是政策中提到的大规模农网升级改造投资，为农村地区的分布式光伏项目（如“千家万户沐光行动”相关项目 ）扫除了一个关键的接入障碍。农村地区拥有广阔的屋顶和土地资源，是分布式光伏的重要潜在市场，电网基础设施的改善将显著提升这些地区项目的可行性和经济性，为公司拓展相关市场提供了良好的外部条件。</p>`
            },
            'policy-3': {
                title: '《分布式光伏发电开发建设管理办法》政策解读',
                content: `<p>这是指导当前及未来一段时期我国分布式光伏项目开发建设的纲领性文件。其精细化的分类管理和明确的各方责任，有助于规范市场秩序，保障项目质量。对电网承载力评估和“四可”能力的强调，反映出随着分布式光伏规模的快速增长，电网の适应性和项目的可调控性已成为管理部门关注的焦点。对于中节能实业公司而言，深入理解并严格执行该办法是所有分布式光伏项目顺利推进的前提。例如，针对不同类型的工商业分布式光伏项目，其自用电量比例要求、并网电压等级限制等均有差异，这直接影响项目设计和经济效益测算。这种细致入微的监管方式，意味着行业正从粗放式增长向高质量发展转变，要求项目开发者具备更强的专业能力和精细化管理水平，以适应不同场景下的具体规定，确保项目的合规性和盈利能力。</p>`
            },
            'policy-4': {
                title: '《源网荷储一体化试点实施细则》政策解读',
                content: `<p>山东省发布的《源网荷储一体化试点实施细则》标志着其在推动能源系统向更高效、灵活和市场化方向转型方面迈出了重要一步。该细则通过清晰界定“就地就近消纳”、“绿电交易”、“虚拟电厂”和“分布式自发自用”四种试点模式，为不同应用场景下的源网荷储项目提供了明确的实施路径和发展指引。</p>
                         <p><strong>“就地就近消纳”模式：</strong>精准对接了出口型企业等对绿色电力有明确需求的大用户，通过设定不低于2亿千瓦时的新能源消纳能力和50%的新能源电量消纳占比目标，有力推动了可再生能源在本地的高效利用，有助于降低企业用能成本和碳足迹。</p>
                         <p><strong>“绿电交易”模式：</strong>对项目的技术能力提出了较高要求，包括电力数据实时监控、功率自动控制及数据预测等功能，这不仅将促进相关先进技术的集成应用，也为绿电的规范化、市场化交易奠定了坚实基础。</p>
                         <p><strong>“虚拟电厂”模式：</strong>是该细则的核心亮点之一。它允许聚合省内不同区域、未纳入统一调度管理的分布式电源、用户侧储能及可调节负荷等资源，作为独立主体参与电力市场交易。这与国家层面大力发展虚拟电厂的导向高度一致，将极大提升电网の灵活性和新能源の接纳能力，并为虚拟电厂运营商探索多元化商业模式创造了广阔空间。聚合资源由虚拟电厂运营商自主管理，也体现了市场化运作の原则。</p>
                         <p><strong>“分布式自发自用”模式：</strong>则继续巩固和规范了分布式能源最基本の应用形式，鼓励用户侧の能源自给自足。</p>
                         <p>总体而言，山东省此项细则具有很强の可操作性和前瞻性。它不仅为省内“源网荷储一体化”项目の规范、有序发展提供了坚实の政策保障，也为其他地区探索能源系统转型新路径、构建新型电力系统提供了宝贵の实践经验和借鉴模式，预计将有力促进区域能源结构の持续优化和能源产业の创新发展。</p>`
            },
            'policy-5': {
                title: '《江苏省绿色建筑发展条例》政策解读',
                content: `<p>江苏省此举在全国范围内具有领先意义，标志着地方绿色建筑管理从侧重运营阶段节能向覆盖全生命周期的碳排放管控深化。这对建筑材料生产、建筑设计、施工乃至后期运维都将产生深远影响。特别是“产品碳标识认证”和“产品碳足迹管理体系”的提出，将直接引导市场选择低碳建材和产品，为相关绿色产业带来发展机遇。中节能实业公司在江苏区域的项目，必须严格遵守这些新增的碳管理规定，这意味着在项目前期策划和设计阶段就需要进行更详细的碳排放核算和材料碳足迹评估。这既是挑战，也是公司展示其在低碳建筑领域专业能力、推广应用低碳技术的机会，例如优先选用具有碳标签认证的BIPV产品或低碳混凝土等。</p>`
            },
            'policy-6': {
                title: '《广东省公共机构节能降碳工作安排》政策解读',
                content: `<p>广东省通过公共机构的示范引领作用，推动分布式光伏等可再生能源的应用。这一方面为分布式光伏项目在公共建筑（如学校、医院、政府办公楼等）の拓展创造了稳定的市场需求；另一方面，鼓励购买绿证也为更大范围内的可再生能源项目提供了间接支持。这种“直接安装”与“间接购买”并举的方式，为公共机构实现绿色能源消费目标提供了灵活性。对于中节能实业公司而言，可以针对广东省公共机构的市场特点，提供包括屋顶光伏勘察设计、投资建设、运营维护以及绿证交易咨询在内的一揽子能源服务方案。</p>`
            },
            'policy-7': {
                title: '《深圳市BIPV和“光储直柔”建筑试点申报》政策解读',
                content: `<p>深圳市作为改革开放的前沿阵地，在推动绿色建筑技术创新方面再次走在前列。通过设立专项试点项目，政府旨在鼓励和验证BIPV、“光储直柔”等前沿技术的实际应用效果和推广价值。对于中节能实业公司而言，若在这些领域拥有成熟技术或解决方案，积极参与此类试点项目不仅能获得政策支持和市场认可，更能积累宝贵的项目经验，为后续更大规模的市场推广奠定基础。深圳对这些技术的重视，也预示着其未来可能在新建建筑标准或城市更新项目中对这些技术提出更高要求。</p>`
            },
            'policy-8': {
                title: '《无锡市零碳园区建设三年行动方案》政策解读',
                content: `<p>无锡市聚焦产业园区这一重点碳排放单元，提出系统性的零碳转型方案。这为综合能源服务商提供了广阔的市场空间，包括大规模分布式光伏的部署、储能系统的配置、智慧能源管理平台的建设等。</p>`
            }
        };

        const multiItemData = {
            techCases: [
                {
                    title: '案例1: 园区厂房——重庆长安汽车渝北工厂20MW光伏车棚项目',
                    content: `<p>该项目作为重庆市“百万千瓦屋顶分布式光伏”建设行动的一部分，虽早于本报告期，但其模式对当前仍具指导意义。项目利用厂区大规模建设光伏车棚，实现了空间综合利用和“光储充”一体化应用。年发电量约2200万千瓦时，可满足工厂约30%的用电需求，每年节约电费超过1200万元，减少二氧化碳排放约1.8万吨。这一案例充分展示了大型制造企业利用自有闲置空间（如车顶、停车场）建设分布式光伏的巨大潜力，并通过“光储充”模式提升能源自给率、降低运营成本，并实现显著的环保效益。重庆市通过专项行动和创新的金融政策（如“阳光贷”）为这类项目提供了有力支持 ，为其他地区推广类似项目提供了借鉴。</p>`,
                    link: 'https://www.escn.com.cn/news/show-2107268.html'
                },
                {
                    title: '案例2: 公共基础设施——国网山西大同1.2万户分布式光伏柔性群调群控试点',
                    content: `<p>国网山西省电力公司于2025年4月宣布成功对大同市1.2万户分布式光伏发电用户实现柔性群调群控，其中3284户具备秒级响应能力。该试点通过提升计量设备采集频率（从15分钟/次缩短至1分钟/次）、在用户侧加装分布式光伏接入单元、打通通信协议并制定差异化调控策略等技术手段，实现了对大规模户用光伏的远程柔性调控 。此项目是解决高比例分布式光伏接入配电网所带来消纳和调控挑战的关键技术示范，直接应用了“分布式光伏接入配电网关键支撑技术”，对于保障电网安全稳定运行、提升新能源消纳水平具有重要意义。</p>`,
                    link: 'http://www.sx.sgcc.com.cn/homepage/wbw/articles/202504/a1162700.html'
                },
                {
                    title: '案例3: 广西虚拟电厂(VPP)含分布式光伏实时调控技术验证项目',
                    content: `<p>2025年5月，广西电网公司联合典型的分布式储能和光伏企业，成功完成了虚拟电厂调控平台与分布式资源的实时调控技术验证，聚合容量达2.3MW 。虚拟电厂作为聚合和优化分布式能源（包括分布式光伏）的关键技术手段，其在广西的成功验证，为后续虚拟电厂纳入电网实时调度体系、促进清洁能源高水平消纳奠定了坚实基础。</p>`,
                    link: 'http://www.gx.xinhuanet.com/20250518/8accc53faaca4e5ea85f3acd44ed4c6f/c.html'
                }
            ],
            gcsfCases: [
                {
                    title: '案例: 特斯联武汉智慧产业园',
                    content: `<p>该产业园成功部署了“光储直柔”系统，并结合智慧能源管理平台和虚拟电厂（VPP）应用，实现了园区超过50%的用电来自光伏发电，年均碳减排达273吨（光伏部分），通过VPP调峰年节约电费约40万元，减少碳排放约1.5万吨。此案例充分展示了“光储直柔”技术在产业园区应用的综合效益。</p>`,
                    link: 'https://www.hb.chinanews.com.cn/news/2025/0605/417685.html'
                }
            ],
            analysisCases: [
                 {
                    title: '案例解析：国网山西大同1.2万户分布式光伏柔性群调群控试点',
                    content: `
                        <img src="https://img.picui.cn/free/2025/06/11/684971aaf4129.jpeg" alt="山西大同大规模地面光伏电站" class="w-full h-auto rounded-lg mb-6 shadow-md object-cover">
                        <p class="font-semibold text-slate-800">2.1.1、项目背景与规模：</p>
                        <p class="text-sm text-slate-700">山西省作为我国重要的能源基地，近年来新能源发展迅速，分布式光伏装机规模持续增长。截至2025年3月底，山西分布式光伏装机已达1077万千瓦，占新能源总装机的16.53%。然而，大规模、分散式的户用光伏接入给配电网的安全稳定运行和电量消纳带来了挑战。在此背景下，国网山西省电力公司在大同市选取了1.2万户分布式光伏用户开展柔性群调群控试点，其中3284户实现了秒级响应能力，旨在探索高比例分布式光伏接入下配电网的智能调控和高效消纳新模式。</p>
                        <p class="font-semibold text-slate-800 mt-4">2.1.2、技术方案深度解析：</p>
                        <p class="text-sm text-slate-700">该试点的核心在于实现对大量分散户用光伏的协同调控。首先，通过修订《分布式光伏发电项目低压发用电合同》，为用户参与调控提供了合约基础。技术层面，将试点台区的计量设备数据采集频率从常规的15分钟/次提升至1分钟/次，为精准调控和实时反馈提供了数据支撑。在用户侧，加装了分布式光伏接入单元，通过打通通信协议，使电网调度系统能够远程、柔性地调节光伏出力。此外，针对不同用户的特性，制定了分类分级的差异化调控策略，以提升用户侧响应的灵活性和调控的精准性。对于部分接入受限和动态调控困难的区域，还探索将分布式光伏的低压多点并网调整为10千伏单点并网，并通过“多合一”控制终端实现集中调控。</p>
                        <p class="font-semibold text-slate-800 mt-4">2.1.3、政策驱动与挑战应对：</p>
                        <p class="text-sm text-slate-700">该项目的实施，是响应国家层面推动新型电力系统建设、提升新能源消纳能力的政策导向。同时也直接回应了《分布式光伏发电开发建设管理办法》中关于电网企业应加强有源配电网规划、建立相应调度运行机制的要求。在实施过程中，可能面临的挑战包括：大量用户侧设备的改造成本和协调难度、通信网络的稳定性和安全性保障、调控策略的优化以及用户对调控的接受度等。通过合同约定、技术升级和差异化策略，项目方正逐步克服这些挑战。</p>
                        <p class="font-semibold text-slate-800 mt-4">2.1.4、成果影响与启示：</p>
                        <p class="text-sm text-slate-700">该试点的成功，初步验证了在高密度户用光伏接入区域实施柔性群调群控技术的可行性和有效性，显著提升了配电网对分布式光伏的接纳和调控能力，为其他地区提供了宝贵经验。未来，国网山西电力计划依托负荷管理系统开发低压分布式光伏统一管理模块，构建“监测-指令-调控”机制 ，这将进一步深化技术的应用。对于中节能实业公司而言，此类电网侧主动提升消纳能力和调控水平的举措，为公司在园区开发分布式光伏项目提供了更稳定的外部环境。同时，也启示公司在项目开发中应更加注重系统的智能化和可调控性，以便更好地融入未来以主动配电网和虚拟电厂为特征的电力系统。</p>
                    `,
                    link: 'http://www.sx.sgcc.com.cn/homepage/wbw/articles/202504/a1162700.html'
                },
                 {
                    title: '案例解析：重庆“百万千瓦屋顶”行动中的典型工厂“光储充”一体化项目',
                    content: `
                        <img src="https://img.picui.cn/free/2025/06/11/684971abc85ce.jpeg" alt="重庆城市屋顶分布式光伏" class="w-full h-auto rounded-lg mb-6 shadow-md object-cover">
                        <p class="font-semibold text-slate-800">2.2.1、项目背景与模式：</p>
                        <p class="text-sm text-slate-700">重庆市于2024年6月启动“百万千瓦屋顶分布式光伏”建设行动，目标到2025年底新增并网100万千瓦以上。截至2025年3月，已新增装机120万千瓦。在此行动推动下，涌现了多种创新开发模式，其中“工厂光储充一体化”是针对工业园区和大型制造企业的有效解决方案。以长安汽车渝北工厂20MW光伏车棚项目为例，该项目充分利用厂区空间，建设大规模分布式光伏，并结合储能和充电设施，形成能源生产、存储和消费的闭环。</p>
                        <p class="font-semibold text-slate-800 mt-4">2.2.2、技术设计与特点：</p>
                        <p class="text-sm text-slate-700">此类项目的核心是将光伏发电系统、储能系统和电动汽车充电桩进行一体化设计和智能化管理。光伏系统（如长安汽车的20MW车棚光伏）为主要能源来源；储能系统用于平抑光伏出力波动、实现峰谷套利、提供备用电源；充电桩则满足厂区内电动汽车的充电需求。整个系统通过能源管理系统（EMS）进行优化调度，实现能源的自发自用最大化和运营成本的最小化。</p>
                        <p class="font-semibold text-slate-800 mt-4">2.2.3、经济可行性与商业模式：</p>
                        <p class="text-sm text-slate-700">项目的经济性主要来源于节省的电费、可能的峰谷电价差收益以及部分地区对储能参与辅助服务的补贴。重庆的案例中提到，长安汽车项目每年可节省电费超过1200万元。商业模式上，除了企业自投自建外，重庆还推广“企业投资+农户屋顶入股”、政府机关引入第三方合同能源管理（EMC）等模式。针对工商业项目，重庆农商行等金融机构推出了“阳光贷”等专项金融产品，降低了项目融资门槛。</p>
                        <p class="font-semibold text-slate-800 mt-4">2.2.4、政策与金融支持：</p>
                        <p class="text-sm text-slate-700">重庆市为推动该行动，出台了“1+N”政策组合拳，建立了项目库，并协调解决项目推进中的问题。金融政策创新是其一大亮点，构建了“政银企民”多方协同投融资机制，将符合条件的项目纳入绿色信贷统计范围，并推出了多种“光伏贷”、“碳收益权质押”等金融工具，部分区县还设立了风险补偿基金。</p>
                        <p class="font-semibold text-slate-800 mt-4">2.2.5、对工业用户的效益与推广价值：</p>
                        <p class="text-sm text-slate-700">对于工业用户而言，“光储充”一体化项目不仅能显著降低能源成本，提高绿电使用比例，助力企业实现碳减排目标和提升绿色品牌形象，还能在一定程度上提升供电可靠性。该模式在工业园区、大型厂房等场景具有很高的推广价值。实业公司在为园区客户提供能源解决方案时，可充分借鉴重庆的经验，结合客户的具体用能特性和当地政策，设计定制化的“光储充”一体化方案，并协助客户对接相关的金融支持政策。</p>
                    `,
                    link: 'https://www.escn.com.cn/news/show-2107268.html'
                }
            ]
        };

        // --- Standard Modal Logic ---
        const modalContainer = document.getElementById('modal-container');
        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');
        const modalFooter = document.getElementById('modal-footer');
        let currentMultiItemType = null;
        let currentMultiItemIndex = 0;

        function openModal() {
            modalContainer.classList.remove('hidden');
            modalContainer.classList.add('flex');
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            modalContainer.classList.add('hidden');
            modalContainer.classList.remove('flex');
            document.body.style.overflow = '';
        }

        document.querySelectorAll('.modal-trigger').forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const modalId = trigger.getAttribute('data-modal-id');
                const data = modalData[modalId];
                if (data) {
                    modalTitle.innerHTML = data.title;
                    modalBody.innerHTML = data.content;
                    modalFooter.classList.add('hidden');
                    openModal();
                }
            });
        });

        document.querySelectorAll('.modal-trigger-multi').forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                currentMultiItemType = trigger.getAttribute('data-modal-type');
                currentMultiItemIndex = parseInt(trigger.getAttribute('data-initial-index') || '0');
                renderMultiItemModal();
                openModal();
            });
        });
        
        function renderMultiItemModal() {
            const items = multiItemData[currentMultiItemType];
            if (!items || items.length === 0) return;
            
            const currentItem = items[currentMultiItemIndex];
            modalTitle.innerHTML = currentItem.title;
            modalBody.innerHTML = currentItem.content;
            
            if(items.length > 1) {
                modalFooter.classList.remove('hidden');
                modalFooter.innerHTML = `
                    <div class="flex justify-between items-center">
                        <button id="prev-item" class="px-4 py-2 bg-slate-200 hover:bg-slate-300 text-slate-800 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                            <i class="fa-solid fa-arrow-left mr-2"></i>上一个案例
                        </button>
                        <div class="flex items-center gap-4">
                            <span class="text-sm text-slate-500">${currentMultiItemIndex + 1} / ${items.length}</span>
                            <a href="${currentItem.link}" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                        </div>
                        <button id="next-item" class="px-4 py-2 bg-emerald-500 hover:bg-emerald-400 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
                            下一个案例<i class="fa-solid fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                `;
                
                const prevBtn = document.getElementById('prev-item');
                const nextBtn = document.getElementById('next-item');
                
                prevBtn.disabled = currentMultiItemIndex === 0;
                nextBtn.disabled = currentMultiItemIndex === items.length - 1;
                
                prevBtn.addEventListener('click', () => {
                    if (currentMultiItemIndex > 0) {
                        currentMultiItemIndex--;
                        renderMultiItemModal();
                    }
                });
                
                nextBtn.addEventListener('click', () => {
                    if (currentMultiItemIndex < items.length - 1) {
                        currentMultiItemIndex++;
                        renderMultiItemModal();
                    }
                });
            } else {
                 modalFooter.classList.remove('hidden');
                 modalFooter.innerHTML = `
                     <div class="flex justify-end">
                         <a href="${currentItem.link}" target="_blank" class="text-sm text-slate-500 hover:text-slate-800 transition-colors">信源 <i class="fa-solid fa-arrow-up-right-from-square text-xs ml-1"></i></a>
                     </div>
                 `;
            }
        }
        
        document.querySelectorAll('.modal-close').forEach(el => el.addEventListener('click', closeModal));

        // --- NEW: Podcast Modal Logic ---
        const podcastBtn = document.getElementById('podcast-btn');
        if (podcastBtn) {
            podcastBtn.addEventListener('click', (e) => {
                e.preventDefault();
                modalTitle.innerHTML = '收听本期月刊播客';
                modalBody.innerHTML = `
                    <div class="text-center space-y-4">
                        <p class="text-slate-600">打开喜马拉雅APP或微信，扫下方二维码收听</p>
                        <img src="https://img.picui.cn/free/2025/06/16/684fca84bac58.png" alt="播客二维码" class="mx-auto w-48 h-48 rounded-lg shadow-md">
                        <p class="text-sm text-slate-500">本播客是由AI生成的当期月刊音频版</p>
                    </div>
                `;
                modalFooter.classList.add('hidden'); // Ensure footer with navigation is hidden
                openModal();
            });
        }
        
        // --- Scroll to Top Button Logic ---
        const scrollTopBtn = document.getElementById('scrollTopBtn');
        window.onscroll = function() {
            if (document.body.scrollTop > 300 || document.documentElement.scrollTop > 300) {
                scrollTopBtn.style.display = "flex";
            } else {
                scrollTopBtn.style.display = "none";
            }
        };

        // --- Scroll Animation Logic ---
        const revealElements = document.querySelectorAll('.reveal-element');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                }
            });
        }, { threshold: 0.1 });
        revealElements.forEach(el => observer.observe(el));

        // --- Issue Selector Modal Logic ---
        const issueSelectorBtnMain = document.getElementById('issue-selector-btn-main');
        const issueModal = document.getElementById('issue-modal');
        const issueModalContent = document.getElementById('issue-modal-content');
        const issueModalBackdrop = document.getElementById('issue-modal-backdrop');
        const issueModalClose = document.getElementById('issue-modal-close');
        
        const yearPrevBtn = document.getElementById('year-prev-btn');
        const yearNextBtn = document.getElementById('year-next-btn');
        const yearDisplay = document.getElementById('year-display');
        const monthWheel = document.getElementById('month-wheel');

        const config = {
            currentYear: 2025,
            publishedIssues: {
                2025: [1, 2, 3, 4, 5],
            },
            availableYears: [2025], 
            currentIssue: 5
        };

        let activeYearIndex = config.availableYears.indexOf(config.currentYear);
        if (activeYearIndex === -1 && config.availableYears.length > 0) {
            activeYearIndex = 0;
        }

        function openIssueModal() {
            issueModal.classList.remove('hidden');
            issueModal.classList.add('flex');
            setTimeout(() => {
                issueModalContent.classList.remove('-translate-x-full');
            }, 10);
        }

        function closeIssueModal() {
            issueModalContent.classList.add('-translate-x-full');
            setTimeout(() => {
                issueModal.classList.add('hidden');
                issueModal.classList.remove('flex');
            }, 500);
        }

        issueSelectorBtnMain.addEventListener('click', openIssueModal);
        issueModalBackdrop.addEventListener('click', closeIssueModal);
        issueModalClose.addEventListener('click', closeIssueModal);

        function updateArrowButtons() {
            if (!yearPrevBtn || !yearNextBtn) return;
            // THEME CHANGE: Classes updated for light theme
            const enabledClasses = ['text-slate-500', 'hover:text-slate-800', 'cursor-pointer'];
            const disabledClasses = ['text-slate-400', 'cursor-not-allowed'];
            
            if (activeYearIndex <= 0) {
                yearPrevBtn.disabled = true;
                yearPrevBtn.classList.remove(...enabledClasses);
                yearPrevBtn.classList.add(...disabledClasses);
            } else {
                yearPrevBtn.disabled = false;
                yearPrevBtn.classList.remove(...disabledClasses);
                yearPrevBtn.classList.add(...enabledClasses);
            }

            if (activeYearIndex >= config.availableYears.length - 1) {
                yearNextBtn.disabled = true;
                yearNextBtn.classList.remove(...enabledClasses);
                yearNextBtn.classList.add(...disabledClasses);
            } else {
                yearNextBtn.disabled = false;
                yearNextBtn.classList.remove(...disabledClasses);
                yearNextBtn.classList.add(...enabledClasses);
            }
        }

        function updateYearDisplay() {
            if (config.availableYears.length > 0) {
                const selectedYear = config.availableYears[activeYearIndex];
                yearDisplay.textContent = `${selectedYear}年`;
                renderMonthWheel(selectedYear);
                updateArrowButtons();
            }
        }
        
        yearPrevBtn.addEventListener('click', () => {
            if (!yearPrevBtn.disabled) {
                activeYearIndex--;
                updateYearDisplay();
            }
        });

        yearNextBtn.addEventListener('click', () => {
             if (!yearNextBtn.disabled) {
                activeYearIndex++;
                updateYearDisplay();
            }
        });

        function renderMonthWheel(year) {
            monthWheel.innerHTML = '';
            const radius = 130;
            const centerX = 192; 
            const centerY = 192; 
            for (let i = 1; i <= 12; i++) {
                const angle = (i - 3) * (Math.PI / 6); 
                const x = centerX + radius * Math.cos(angle);
                const y = centerY + radius * Math.sin(angle);
                const monthEl = document.createElement('div');
                monthEl.textContent = i;
                monthEl.className = 'absolute w-10 h-10 flex items-center justify-center rounded-full text-lg transition-all duration-300 transform -translate-x-1/2 -translate-y-1/2';
                monthEl.style.left = `${x}px`;
                monthEl.style.top = `${y}px`;
                const isPublished = config.publishedIssues[year]?.includes(i);
                const isCurrentIssue = (year === config.currentYear && i === config.currentIssue);
                
                // THEME CHANGE: Month colors updated for light theme
                if (isCurrentIssue) {
                    monthEl.classList.add('bg-emerald-500', 'text-white', 'font-bold', 'cursor-pointer', 'scale-110');
                } else if (isPublished) {
                    monthEl.classList.add('text-slate-800', 'hover:bg-emerald-400/50', 'hover:scale-110', 'cursor-pointer');
                } else {
                    monthEl.classList.add('text-slate-400', 'cursor-not-allowed');
                }
                monthWheel.appendChild(monthEl);
            }
        }
        
        updateYearDisplay();
    });
    </script>
    
    <!-- Three.js Background Script for Spring Theme -->
    <script>
        let scene, camera, renderer, particles, mouse;

        function init() {
            scene = new THREE.Scene();
            camera = new THREE.PerspectiveCamera(60, window.innerWidth / window.innerHeight, 1, 1000);
            camera.position.z = 1;

            const canvas = document.getElementById('bg-canvas');
            renderer = new THREE.WebGLRenderer({
                canvas: canvas,
                alpha: true
            });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

            mouse = new THREE.Vector2();
            document.addEventListener('mousemove', (event) => {
                mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
                mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
            });
            
            createParticles();
            window.addEventListener('resize', onWindowResize);
        }

        function createParticles() {
            const particleCount = 7000;
            const positions = new Float32Array(particleCount * 3);
            const velocities = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);
            const sizes = new Float32Array(particleCount);
            
            const geometry = new THREE.BufferGeometry();
            const colorPalette = [
                new THREE.Color(0x84cc16), // lime-500
                new THREE.Color(0x4ade80), // green-400
                new THREE.Color(0x22c55e)  // green-500
            ];
            
            for (let i = 0; i < particleCount; i++) {
                const i3 = i * 3;

                // Position
                positions[i3] = (Math.random() - 0.5) * 10;
                positions[i3 + 1] = (Math.random() - 0.5) * 10;
                positions[i3 + 2] = (Math.random() - 0.5) * 10;

                // Velocity (randomized for dynamic feel)
                velocities[i3] = 0;
                velocities[i3 + 1] = (Math.random() * 0.0008) + 0.0003; // Dynamic upward drift
                velocities[i3 + 2] = 0;
                
                // Color (random from palette)
                const color = colorPalette[Math.floor(Math.random() * colorPalette.length)];
                colors[i3] = color.r;
                colors[i3 + 1] = color.g;
                colors[i3 + 2] = color.b;

                // MODIFIED: Size (randomized and larger)
                sizes[i] = Math.random() * 0.05 + 0.02;
            }

            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));


            const material = new THREE.ShaderMaterial({
                uniforms: {
                    // No texture needed for squares
                },
                vertexShader: `
                    attribute float size;
                    attribute vec3 color;
                    varying vec3 vColor;
                    void main() {
                        vColor = color;
                        vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );
                        gl_PointSize = size * ( 300.0 / -mvPosition.z );
                        gl_Position = projectionMatrix * mvPosition;
                    }
                `,
                // MODIFIED: Fragment shader to render a solid square
                fragmentShader: `
                    varying vec3 vColor;
                    void main() {
                        gl_FragColor = vec4( vColor, 1.0 );
                    }
                `,
                blending: THREE.NormalBlending,
                depthTest: false,
                transparent: true
            });

            particles = new THREE.Points(geometry, material);
            scene.add(particles);
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }

        function animate() {
            requestAnimationFrame(animate);
            const positions = particles.geometry.attributes.position.array;
            const velocities = particles.geometry.attributes.velocity.array;

            for (let i = 0; i < positions.length / 3; i++) {
                const i3 = i * 3;
                positions[i3 + 1] += velocities[i3 + 1];
                if (positions[i3 + 1] > 5) {
                    positions[i3 + 1] = -5;
                }
            }
            particles.geometry.attributes.position.needsUpdate = true;
            
            const targetRotationX = mouse.y * 0.1;
            const targetRotationY = mouse.x * 0.1;
            particles.rotation.x += (targetRotationX - particles.rotation.x) * 0.05;
            particles.rotation.y += (targetRotationY - particles.rotation.y) * 0.05;
            
            renderer.render(scene, camera);
        }

        init();
        animate();
    </script>
</body>
</html>